#!/usr/bin/env python3
"""
训练PIE-RGB-SAR数据集的语义分割模型
使用OverLoCK backbone + UPernet decode head + FCN auxiliary head
"""

import os
import sys
import argparse
import subprocess

def main():
    parser = argparse.ArgumentParser(description='Train PIE-RGB-SAR semantic segmentation model')
    parser.add_argument('--config', 
                        default='segmentation/configs/overlock/upernet_overlock_small_pie_rgb_sar_8xb8.py',
                        help='train config file path')
    parser.add_argument('--work-dir', 
                        default='work_dirs/upernet_overlock_small_pie_rgb_sar',
                        help='the dir to save logs and models')
    parser.add_argument('--resume-from', 
                        help='the checkpoint file to resume from')
    parser.add_argument('--load-from', 
                        help='the checkpoint file to load weights from')
    parser.add_argument('--gpus', type=int, default=1,
                        help='number of gpus to use')
    parser.add_argument('--seed', type=int, default=42,
                        help='random seed')
    parser.add_argument('--deterministic', action='store_true',
                        help='whether to set deterministic options for CUDNN backend.')
    
    args = parser.parse_args()
    
    # 构建训练命令
    cmd = [
        'python', 'segmentation/train.py',
        args.config,
        '--work-dir', args.work_dir,
        '--seed', str(args.seed),
    ]
    
    if args.resume_from:
        cmd.extend(['--resume-from', args.resume_from])
    
    if args.load_from:
        cmd.extend(['--load-from', args.load_from])
    
    if args.deterministic:
        cmd.append('--deterministic')
    
    if args.gpus > 1:
        # 多GPU训练
        cmd = [
            'python', '-m', 'torch.distributed.launch',
            f'--nproc_per_node={args.gpus}',
            'segmentation/train.py',
            args.config,
            '--work-dir', args.work_dir,
            '--seed', str(args.seed),
            '--launcher', 'pytorch'
        ]
        
        if args.resume_from:
            cmd.extend(['--resume-from', args.resume_from])
        
        if args.load_from:
            cmd.extend(['--load-from', args.load_from])
        
        if args.deterministic:
            cmd.append('--deterministic')
    
    print(f"执行命令: {' '.join(cmd)}")
    
    # 创建工作目录
    os.makedirs(args.work_dir, exist_ok=True)
    
    # 执行训练
    try:
        subprocess.run(cmd, check=True)
    except subprocess.CalledProcessError as e:
        print(f"训练失败: {e}")
        sys.exit(1)
    except KeyboardInterrupt:
        print("训练被用户中断")
        sys.exit(1)

if __name__ == '__main__':
    main()
