#!/usr/bin/env python3
"""
可视化三主干神经网络的数据流
"""

import torch
import torch.nn as nn
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import config
from models.builder import EncoderDecoder as segmodel


def trace_dataflow():
    """追踪三主干网络的数据流"""
    print("=" * 80)
    print("三主干神经网络数据流分析")
    print("=" * 80)
    
    # 创建模型
    criterion = nn.CrossEntropyLoss(ignore_index=config.background)
    model = segmodel(cfg=config, criterion=criterion, norm_layer=nn.BatchNorm2d)
    
    # 获取backbone
    backbone = model.backbone
    
    print("网络结构分析:")
    print(f"- 主干类型: {type(backbone).__name__}")
    print(f"- 层数: {backbone.num_layers}")
    print(f"- 特征维度: {backbone.num_features}")
    print(f"- FRM模块数量: {len(backbone.FRMs)}")
    print(f"- FFM模块数量: {len(backbone.FFMs)}")
    print(f"- GCCM模块数量: {len(backbone.GCCMs)}")
    print(f"- SFS backbone: {type(backbone.sfs_backbone).__name__}")
    
    print("\n" + "=" * 80)
    print("数据流追踪 (每个Stage)")
    print("=" * 80)
    
    # 创建测试输入
    batch_size = 1
    rgb_input = torch.randn(batch_size, 3, 224, 224)
    depth_input = torch.randn(batch_size, 3, 224, 224)
    
    print(f"输入:")
    print(f"  RGB: {rgb_input.shape}")
    print(f"  Depth: {depth_input.shape}")
    
    # 手动追踪forward过程
    with torch.no_grad():
        # SFS backbone处理
        sfs_outs = backbone.sfs_backbone(rgb_input)
        print(f"\nSFS Backbone输出:")
        for i, sfs_out in enumerate(sfs_outs):
            print(f"  Stage {i}: {sfs_out.shape}")
        
        # Patch embedding
        x = backbone.patch_embed(rgb_input)
        x_d = backbone.patch_embed_d(depth_input)
        
        print(f"\nPatch Embedding后:")
        print(f"  RGB: {x.shape}")
        print(f"  Depth: {x_d.shape}")
        
        # 转换为序列格式
        Wh, Ww = x.size(2), x.size(3)
        x = x.flatten(2).transpose(1, 2)
        x_d = x_d.flatten(2).transpose(1, 2)
        x = backbone.pos_drop(x)
        x_d = backbone.pos_drop_d(x_d)
        
        print(f"\n序列格式转换后:")
        print(f"  RGB: {x.shape}")
        print(f"  Depth: {x_d.shape}")
        print(f"  空间尺寸: {Wh} x {Ww}")
        
        # 逐层处理
        outs = []
        sfs_stage_idx = 0
        
        for i in range(backbone.num_layers):
            print(f"\n{'='*20} Stage {i} {'='*20}")
            
            # 1. Swin Transformer层处理
            layer = backbone.layers[i]
            layer_d = backbone.layers_d[i]
            
            print(f"Stage {i} 输入:")
            print(f"  RGB: {x.shape}")
            print(f"  Depth: {x_d.shape}")
            
            x, H, W = layer(x, Wh, Ww)
            x_d, _, _ = layer_d(x_d, Wh, Ww)
            
            print(f"Swin层处理后:")
            print(f"  RGB: {x.shape}")
            print(f"  Depth: {x_d.shape}")
            print(f"  空间尺寸: {H} x {W}")
            
            # 2. FRM (Cross-Modal Information Distillation)
            x_frm_in = x.view(-1, H, W, backbone.num_features[i]).permute(0, 3, 1, 2).contiguous()
            x_d_frm_in = x_d.view(-1, H, W, backbone.num_features[i]).permute(0, 3, 1, 2).contiguous()
            
            print(f"FRM输入 (转换为BCHW格式):")
            print(f"  RGB: {x_frm_in.shape}")
            print(f"  Depth: {x_d_frm_in.shape}")
            
            x_frm_out, x_d_frm_out = backbone.FRMs[i](x_frm_in, x_d_frm_in)
            
            print(f"FRM输出:")
            print(f"  RGB: {x_frm_out.shape}")
            print(f"  Depth: {x_d_frm_out.shape}")
            
            # 转换回序列格式用于下一层
            x = x_frm_out.flatten(2).transpose(1, 2)
            x_d = x_d_frm_out.flatten(2).transpose(1, 2)
            
            print(f"转换回序列格式 (用于下一层):")
            print(f"  RGB: {x.shape}")
            print(f"  Depth: {x_d.shape}")
            
            # 保存当前层输出用于最终融合
            x_out, x_out_d = x, x_d
            
            # 3. 下采样 (除了最后一层)
            if i < backbone.num_layers - 1:
                x = backbone.downsamples[i](x, H, W)
                x_d = backbone.downsamples_d[i](x_d, H, W)
                Wh, Ww = (H + 1) // 2, (W + 1) // 2
                
                print(f"下采样后 (用于下一层):")
                print(f"  RGB: {x.shape}")
                print(f"  Depth: {x_d.shape}")
                print(f"  新空间尺寸: {Wh} x {Ww}")
            
            # 4. 输出层处理
            if i in backbone.out_indices:
                print(f"\n--- 输出层 {i} 处理 ---")
                
                # 归一化
                norm_layer = getattr(backbone, f'norm{i}')
                x_out_norm = norm_layer(x_out)
                
                norm_layer_d = getattr(backbone, f'norm_d{i}')
                x_out_d_norm = norm_layer_d(x_out_d)
                
                print(f"归一化后:")
                print(f"  RGB: {x_out_norm.shape}")
                print(f"  Depth: {x_out_d_norm.shape}")
                
                # 转换为BCHW格式用于融合
                x_out_final = x_out_norm.view(-1, H, W, backbone.num_features[i]).permute(0, 3, 1, 2).contiguous()
                x_out_d_final = x_out_d_norm.view(-1, H, W, backbone.num_features[i]).permute(0, 3, 1, 2).contiguous()
                
                print(f"转换为BCHW格式:")
                print(f"  RGB: {x_out_final.shape}")
                print(f"  Depth: {x_out_d_final.shape}")
                
                # 获取对应的SFS特征
                x_sfs = sfs_outs[sfs_stage_idx]
                sfs_stage_idx += 1
                
                print(f"对应SFS特征: {x_sfs.shape}")
                
                # FFM融合RGB和Depth
                fused_rd = backbone.FFMs[i](x_out_final, x_out_d_final)
                print(f"FFM融合后 (RGB+Depth): {fused_rd.shape}")
                
                # GCCM融合RGB-Depth结果与SFS
                final_out = backbone.GCCMs[i](fused_rd, x_sfs)
                print(f"GCCM融合后 (最终输出): {final_out.shape}")
                
                outs.append(final_out)
        
        print(f"\n{'='*80}")
        print("最终输出汇总:")
        for i, out in enumerate(outs):
            print(f"  Stage {i}: {out.shape}")
        
        return outs


def analyze_fusion_strategy():
    """分析融合策略"""
    print("\n" + "=" * 80)
    print("融合策略分析")
    print("=" * 80)
    
    print("当前三主干网络的融合策略:")
    print()
    print("1. 双主干内部融合:")
    print("   RGB → Swin层 → FRM ↘")
    print("   Depth → Swin层 → FRM ↗ → 融合特征 → 下一层")
    print()
    print("2. 三主干最终融合:")
    print("   RGB+Depth (经过FRM) → 归一化 → FFM融合 ↘")
    print("   SFS独立处理 → 对应stage输出 ↗ → GCCM融合 → 最终输出")
    print()
    print("数据流验证:")
    print("✓ 双主干相邻stage间: 上个stage的FRM输出 → 下个stage的输入")
    print("✓ 与SFS融合: 双主干FFM输出 + SFS对应stage输出 → GCCM → 最终输出")
    print()
    print("这种设计的优势:")
    print("- FRM确保双主干在每个stage都有充分的跨模态信息交换")
    print("- FFM先融合双主干的互补信息")
    print("- GCCM最后融合双主干结果与SFS的频域信息")
    print("- 三层融合策略：跨模态 → 双模态 → 三模态")


if __name__ == "__main__":
    trace_dataflow()
    analyze_fusion_strategy()
