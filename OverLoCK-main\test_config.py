#!/usr/bin/env python3
"""
测试PIE-RGB-SAR配置是否正确
"""

import sys
import os

# 添加segmentation路径
sys.path.insert(0, 'segmentation')

def test_config():
    """测试配置文件是否正确"""
    print("=== 测试配置文件 ===")
    
    try:
        try:
            from mmcv import Config
        except ImportError:
            from mmengine import Config
        
        # 加载配置文件
        config_path = 'segmentation/configs/overlock/upernet_overlock_small_pie_rgb_sar_8xb8.py'
        cfg = Config.fromfile(config_path)
        
        print("✅ 配置文件加载成功")
        
        # 检查关键配置
        print(f"数据集类型: {cfg.data.train.type}")
        print(f"数据根目录: {cfg.data.train.data_root}")
        print(f"批次大小: {cfg.data.samples_per_gpu}")
        print(f"工作进程数: {cfg.data.workers_per_gpu}")
        
        # 检查模型配置
        print(f"模型类型: {cfg.model.type}")
        print(f"Backbone类型: {cfg.model.backbone.type}")
        print(f"解码头类型: {cfg.model.decode_head.type}")
        print(f"解码头输入通道: {cfg.model.decode_head.in_channels}")
        print(f"辅助头类型: {cfg.model.auxiliary_head.type}")
        print(f"辅助头输入通道: {cfg.model.auxiliary_head.in_channels}")
        print(f"类别数: {cfg.model.decode_head.num_classes}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置文件测试失败: {e}")
        return False

def test_model_creation():
    """测试模型创建"""
    print("\n=== 测试模型创建 ===")
    print("⚠️ 跳过模型创建测试（需要完整的mmseg环境）")
    return True

def test_dataset_loading():
    """测试数据集加载"""
    print("\n=== 测试数据集加载 ===")
    print("⚠️ 跳过数据集加载测试（需要完整的mmseg环境）")
    return True

def main():
    print("PIE-RGB-SAR 配置测试工具")
    print("=" * 50)
    
    success = True
    
    # 测试配置文件
    success &= test_config()
    
    # 测试模型创建
    success &= test_model_creation()
    
    # 测试数据集加载
    success &= test_dataset_loading()
    
    print("\n" + "=" * 50)
    if success:
        print("✅ 所有测试通过！配置正确，可以开始训练。")
    else:
        print("❌ 部分测试失败！请检查配置。")
    
    return success

if __name__ == '__main__':
    main()
