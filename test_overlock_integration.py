"""
测试OverLoCK主干集成到三主干网络的脚本
"""

import torch
import torch.nn as nn
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.abspath('.'))

from config import config as cfg
from models.encoders.dual_swin import TripleSwinTransformer
from models.encoders.overlock_backbone import OverLoCKBackbone


def test_overlock_backbone():
    """测试OverLoCK主干网络"""
    print("\n" + "=" * 60)
    print("测试 OverLoCK Backbone")
    print("=" * 60)
    
    # 创建OverLoCK主干
    overlock_backbone = OverLoCKBackbone(
        depth=[6, 6, 8, 3],
        sub_depth=[16, 3],
        in_chans=3,
        embed_dim=[64, 128, 320, 512],
        kernel_size=[17, 15, 13, 7],
        mlp_ratio=[4, 4, 4, 4],
        sub_num_heads=[8, 16],
        sub_mlp_ratio=[3, 3],
        ls_init_value=[None, None, 1, 1],
        res_scale=True,
        smk_size=5,
        deploy=False,
        use_gemm=True,
        use_ds=False,
        drop_rate=0,
        drop_path_rate=0.2,
        num_classes=0,
        use_checkpoint=[0, 0, 0, 0],
        out_indices=(0, 1, 2, 3),
        pretrained_path=cfg.overlock_backbone_config.get('pretrained_path', None)
    )
    
    # 测试输入
    x = torch.randn(2, 3, 256, 256)
    print(f"输入形状: {x.shape}")
    
    # 前向传播
    with torch.no_grad():
        outputs = overlock_backbone(x)
    
    print(f"输出阶段数: {len(outputs)}")
    for i, out in enumerate(outputs):
        print(f"阶段 {i} 输出形状: {out.shape}")
    
    return outputs


def test_triple_swin_with_overlock():
    """测试集成OverLoCK的三主干网络"""
    print("\n" + "=" * 60)
    print("测试 Triple Swin Transformer with OverLoCK")
    print("=" * 60)
    
    # 创建三主干模型，使用OverLoCK配置
    model = TripleSwinTransformer(
        pretrain_img_size=224,
        patch_size=4,
        in_chans=3,
        embed_dim=96,
        depths=[2, 2, 6, 2],
        num_heads=[3, 6, 12, 24],
        window_size=7,
        out_indices=(0, 1, 2, 3),
        overlock_config=cfg.overlock_backbone_config
    )
    
    # 测试输入
    x_rgb = torch.randn(2, 3, 256, 256)
    x_sar = torch.randn(2, 3, 256, 256)
    
    print(f"RGB 输入形状: {x_rgb.shape}")
    print(f"SAR 输入形状: {x_sar.shape}")
    
    # 前向传播
    with torch.no_grad():
        outputs = model(x_rgb, x_sar)
    
    print(f"融合后输出阶段数: {len(outputs)}")
    for i, out in enumerate(outputs):
        print(f"阶段 {i} 融合输出形状: {out.shape}")
    
    return outputs


def test_model_creation():
    """测试通过builder创建模型"""
    print("\n" + "=" * 60)
    print("测试 Model Builder")
    print("=" * 60)
    
    try:
        from models.builder import EncoderDecoder
        
        # 创建损失函数
        criterion = nn.CrossEntropyLoss(reduction='mean', ignore_index=255)
        
        # 创建模型
        model = EncoderDecoder(cfg=cfg, criterion=criterion, norm_layer=nn.BatchNorm2d)
        
        print("✅ 模型创建成功")
        print(f"模型类型: {type(model).__name__}")
        print(f"主干网络类型: {type(model.backbone).__name__}")
        print(f"解码器类型: {type(model.decode_head).__name__}")
        print(f"通道配置: {model.channels}")
        
        # 测试前向传播
        x_rgb = torch.randn(1, 3, 256, 256)
        x_sar = torch.randn(1, 3, 256, 256)
        
        with torch.no_grad():
            output = model(x_rgb, x_sar)
        
        print(f"模型输出形状: {output.shape}")
        print("✅ 前向传播测试成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 模型创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_pretrained_weights():
    """测试预训练权重加载"""
    print("\n" + "=" * 60)
    print("测试 OverLoCK 预训练权重加载")
    print("=" * 60)
    
    pretrained_path = cfg.overlock_backbone_config.get('pretrained_path', None)
    
    if pretrained_path and os.path.exists(pretrained_path):
        print(f"✅ 预训练权重文件存在: {pretrained_path}")
        
        try:
            # 测试权重加载
            checkpoint = torch.load(pretrained_path, map_location='cpu')
            print(f"✅ 权重文件加载成功")
            
            if 'model' in checkpoint:
                state_dict = checkpoint['model']
                print("权重格式: checkpoint['model']")
            elif 'state_dict' in checkpoint:
                state_dict = checkpoint['state_dict']
                print("权重格式: checkpoint['state_dict']")
            else:
                state_dict = checkpoint
                print("权重格式: 直接字典")
            
            print(f"权重键数量: {len(state_dict)}")
            print("前5个权重键:")
            for i, key in enumerate(list(state_dict.keys())[:5]):
                print(f"  {i+1}. {key}: {state_dict[key].shape}")
            
            return True
            
        except Exception as e:
            print(f"❌ 权重加载失败: {e}")
            return False
    else:
        print(f"❌ 预训练权重文件不存在: {pretrained_path}")
        return False


def main():
    """主测试函数"""
    print("开始测试 OverLoCK 主干集成...")
    
    # 测试1: OverLoCK主干网络
    try:
        test_overlock_backbone()
        print("✅ OverLoCK主干测试通过")
    except Exception as e:
        print(f"❌ OverLoCK主干测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    # 测试2: 预训练权重
    try:
        test_pretrained_weights()
        print("✅ 预训练权重测试完成")
    except Exception as e:
        print(f"❌ 预训练权重测试失败: {e}")
    
    # 测试3: 三主干网络集成
    try:
        test_triple_swin_with_overlock()
        print("✅ 三主干网络集成测试通过")
    except Exception as e:
        print(f"❌ 三主干网络集成测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    # 测试4: 模型构建器
    try:
        success = test_model_creation()
        if success:
            print("✅ 模型构建器测试通过")
        else:
            print("❌ 模型构建器测试失败")
    except Exception as e:
        print(f"❌ 模型构建器测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "=" * 60)
    print("测试完成！")
    print("=" * 60)


if __name__ == "__main__":
    main()
