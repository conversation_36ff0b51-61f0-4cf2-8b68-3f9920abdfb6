import torch
import torch.nn as nn
import torch.nn.functional as F
from timm.models.layers import DropPath, to_2tuple, trunc_normal_
import numpy as np

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

# 由于文件名包含连字符，需要使用 importlib 导入
import importlib.util
spec = importlib.util.spec_from_file_location("sfs_conv", os.path.join(os.path.dirname(__file__), '..', '..', 'modules', 'SFS-Conv.py'))
sfs_conv_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(sfs_conv_module)
SFS_Conv = sfs_conv_module.SFS_Conv


class SFSPatchEmbed(nn.Module):
    """Image to Patch Embedding using SFS-Conv"""
    
    def __init__(self, patch_size=4, in_chans=3, embed_dim=96, norm_layer=None):
        super().__init__()
        patch_size = to_2tuple(patch_size)
        self.patch_size = patch_size
        self.in_chans = in_chans
        self.embed_dim = embed_dim
        
        # 使用SFS-Conv进行patch embedding
        self.proj = nn.Conv2d(in_chans, embed_dim, kernel_size=patch_size, stride=patch_size)
        self.sfs_conv = SFS_Conv(embed_dim, embed_dim)
        
        if norm_layer is not None:
            self.norm = norm_layer(embed_dim)
        else:
            self.norm = None
    
    def forward(self, x):
        """Forward function."""
        # padding
        _, _, H, W = x.size()
        if W % self.patch_size[1] != 0:
            x = F.pad(x, (0, self.patch_size[1] - W % self.patch_size[1]))
        if H % self.patch_size[0] != 0:
            x = F.pad(x, (0, 0, 0, self.patch_size[0] - H % self.patch_size[0]))
        
        x = self.proj(x)  # B C Wh Ww
        # 注意：SFS-Conv在后续的SFSBlock中应用，这里不需要再次应用
        
        if self.norm is not None:
            Wh, Ww = x.size(2), x.size(3)
            x = x.flatten(2).transpose(1, 2)
            x = self.norm(x)
            x = x.transpose(1, 2).view(-1, self.embed_dim, Wh, Ww)
        
        return x


class SFSBlock(nn.Module):
    """SFS Block for feature processing"""
    
    def __init__(self, dim, drop_path=0.):
        super().__init__()
        self.sfs_conv = SFS_Conv(dim, dim)
        self.drop_path = DropPath(drop_path) if drop_path > 0. else nn.Identity()
        self.norm = nn.BatchNorm2d(dim)
        
    def forward(self, x):
        shortcut = x
        x = self.norm(x)
        x = self.sfs_conv(x)
        x = shortcut + self.drop_path(x)
        return x


class SFSDownsample(nn.Module):
    """Downsampling layer using SFS-Conv"""
    
    def __init__(self, dim, norm_layer=nn.LayerNorm):
        super().__init__()
        self.dim = dim
        self.reduction = nn.Linear(4 * dim, 2 * dim, bias=False)
        self.norm = norm_layer(4 * dim)
        
    def forward(self, x, H, W):
        """Forward function.
        Args:
            x: Input feature, tensor size (B, H*W, C).
            H, W: Spatial resolution of the input feature.
        """
        B, L, C = x.shape
        assert L == H * W, "input feature has wrong size"
        
        x = x.view(B, H, W, C)
        
        # padding
        pad_input = (H % 2 == 1) or (W % 2 == 1)
        if pad_input:
            x = F.pad(x, (0, 0, 0, W % 2, 0, H % 2))
        
        x0 = x[:, 0::2, 0::2, :]  # B H/2 W/2 C
        x1 = x[:, 1::2, 0::2, :]  # B H/2 W/2 C
        x2 = x[:, 0::2, 1::2, :]  # B H/2 W/2 C
        x3 = x[:, 1::2, 1::2, :]  # B H/2 W/2 C
        x = torch.cat([x0, x1, x2, x3], -1)  # B H/2 W/2 4*C
        x = x.view(B, -1, 4 * C)  # B H/2*W/2 4*C
        
        x = self.norm(x)
        x = self.reduction(x)
        
        return x


class SFSLayer(nn.Module):
    """A basic SFS layer for one stage"""
    
    def __init__(self, dim, depth, drop_path=0.):
        super().__init__()
        self.depth = depth
        
        # build blocks
        self.blocks = nn.ModuleList([
            SFSBlock(dim=dim, drop_path=drop_path[i] if isinstance(drop_path, list) else drop_path)
            for i in range(depth)
        ])
    
    def forward(self, x, H, W):
        """Forward function.
        Args:
            x: Input feature, tensor size (B, H*W, C).
            H, W: Spatial resolution of the input feature.
        """
        # 转换为 (B, C, H, W) 格式进行SFS处理
        B, L, C = x.shape
        x = x.view(B, H, W, C).permute(0, 3, 1, 2).contiguous()
        
        for blk in self.blocks:
            x = blk(x)
        
        # 转换回 (B, H*W, C) 格式
        x = x.permute(0, 2, 3, 1).contiguous().view(B, H * W, C)
        return x, H, W


class SFSBackbone(nn.Module):
    """SFS Backbone Network
    
    A four-stage backbone network using SFS-Conv modules that maintains
    the same input/output tensor shapes as dual_swin.py
    """
    
    def __init__(self,
                 patch_size=4,
                 in_chans=3,
                 embed_dim=96,
                 depths=[2, 2, 6, 2],
                 drop_rate=0.,
                 drop_path_rate=0.2,
                 norm_layer=nn.LayerNorm,
                 out_indices=(0, 1, 2, 3)):
        super().__init__()
        
        self.num_layers = len(depths)
        self.embed_dim = embed_dim
        self.out_indices = out_indices
        
        # patch embedding
        self.patch_embed = SFSPatchEmbed(
            patch_size=patch_size, in_chans=in_chans, embed_dim=embed_dim,
            norm_layer=norm_layer)
        
        self.pos_drop = nn.Dropout(p=drop_rate)
        
        # stochastic depth
        dpr = [x.item() for x in torch.linspace(0, drop_path_rate, sum(depths))]
        
        # build layers
        self.layers = nn.ModuleList()
        self.downsamples = nn.ModuleList()
        
        for i_layer in range(self.num_layers):
            layer = SFSLayer(
                dim=int(embed_dim * 2 ** i_layer),
                depth=depths[i_layer],
                drop_path=dpr[sum(depths[:i_layer]):sum(depths[:i_layer + 1])])
            self.layers.append(layer)
            
            # patch merging layer
            if i_layer < self.num_layers - 1:
                downsample = SFSDownsample(dim=int(embed_dim * 2 ** i_layer), norm_layer=norm_layer)
                self.downsamples.append(downsample)
        
        num_features = [int(embed_dim * 2 ** i) for i in range(self.num_layers)]
        self.num_features = num_features
        
        # add a norm layer for each output
        for i_layer in out_indices:
            layer = norm_layer(num_features[i_layer])
            layer_name = f'norm{i_layer}'
            self.add_module(layer_name, layer)
    
    def forward(self, x):
        """Forward function."""
        x = self.patch_embed(x)
        
        Wh, Ww = x.size(2), x.size(3)
        x = x.flatten(2).transpose(1, 2)
        x = self.pos_drop(x)
        
        outs = []
        for i in range(self.num_layers):
            layer = self.layers[i]
            
            x, H, W = layer(x, Wh, Ww)
            
            if i in self.out_indices:
                norm_layer = getattr(self, f'norm{i}')
                x_out = norm_layer(x)
                x_out = x_out.view(-1, H, W, self.num_features[i]).permute(0, 3, 1, 2).contiguous()
                outs.append(x_out)
            
            if i < self.num_layers - 1:
                x = self.downsamples[i](x, H, W)
                Wh, Ww = (H + 1) // 2, (W + 1) // 2
        
        return tuple(outs)
