import os
import os.path as osp
import sys
import time
import numpy as np
from easydict import EasyDict as edict

C = edict()
config = C
cfg = C

C.seed = 12345

# 在Windows系统上使用cd命令代替pwd
if os.name == 'nt':  # Windows系统
    remoteip = os.popen('cd').read()
else:
    remoteip = os.popen('pwd').read()
C.root_dir = os.path.abspath(os.path.join(os.getcwd(), './'))
C.abs_dir = osp.realpath(".")

# Dataset config
"""Dataset Path"""
C.dataset_name = 'PIE-RGB-SAR'
C.dataset_path = osp.join(C.root_dir, 'datasets', 'PIE-RGB-SAR')
C.rgb_root_folder = osp.join(C.dataset_path, 'RGB')
C.rgb_format = '.tif'
C.gt_root_folder = osp.join(C.dataset_path, 'Label')
C.gt_format = '.tif'
C.gt_transform = True
# True when label 0 is invalid, you can also modify the function _transform_gt in dataloader.RGBXDataset
# True for most dataset valid, Faslse for MFNet(?)
C.x_root_folder = osp.join(C.dataset_path, 'SAR')
C.x_format = '.tif'
C.x_is_single_channel = True # 设置为True，因为SAR图像是单通道的
C.train_source = osp.join(C.dataset_path, "train.txt")
C.eval_source = osp.join(C.dataset_path, "val.txt")
C.is_test = False
C.num_train_imgs = 2432  # 数据集重新划分，训练集：验证集 = 1：1
C.num_eval_imgs = 2433    # 数据集重新划分，训练集：验证集 = 1：1
C.num_classes = 6        # 根据标签分析，有6个类别（0-5）
C.class_names = ['background', 'building', 'farmland', 'forest', 'water', 'road']  # 根据实际类别含义修改

"""Image Config"""
C.background = 255  # 设置为255，与RGBXDataset._gt_transform中的处理一致
C.image_height = 256  # 调整为更合适的尺寸
C.image_width = 256
C.norm_mean = np.array([0.348, 0.370, 0.309])  # 根据PIE-RGB-SAR数据集统计得到
C.norm_std = np.array([0.197, 0.174, 0.171])   # 根据PIE-RGB-SAR数据集统计得到

""" Settings for network, this would be different for each kind of model"""
C.backbone = 'swin_s' # Using Swin Transformer Small model (now with triple backbone)
C.pretrained_model = None  # 设置为None，从头开始训练三主干网络
# C.pretrained_model = osp.join(C.root_dir, 'swin_trans', 'swin_small_patch4_window7_224_22k.pth')  # 如果有预训练模型可以取消注释
C.decoder = 'UPernet'  # 改为UPerNet decoder
C.decoder_embed_dim = 512  # UPerNet使用channels参数，但保留此配置以兼容
C.upernet_channels = 512  # UPerNet的通道数配置
C.optimizer = 'AdamW'

# Triple Backbone Configuration
C.use_triple_backbone = True  # 启用三主干架构
C.overlock_backbone_config = {
    'depth': [6, 6, 8, 3],
    'sub_depth': [12, 2],  # 从[16,3]减少到[12,2]以提升速度
    'in_chans': 3,
    'embed_dim': [64, 128, 320, 512],
    'kernel_size': [15, 13, 11, 7],  # 适度减小核大小以提升速度
    'mlp_ratio': [4, 4, 4, 4],
    'sub_num_heads': [8, 16],
    'sub_mlp_ratio': [3, 3],
    'ls_init_value': [None, None, 1, 1],
    'res_scale': True,
    'smk_size': 5,
    'deploy': False,
    'use_gemm': True,
    'use_ds': False,  # 不使用深度监督，因为我们只需要特征提取
    'drop_rate': 0,
    'drop_path_rate': 0.2,
    'num_classes': 0,  # 设置为0，因为我们只需要特征提取
    'use_checkpoint': [2, 2, 4, 1],  # 启用梯度检查点以节省内存
    'pretrained_path': osp.join(C.root_dir, 'overlock', 'overlock_s_in1k_224.pth')
}

# Feature Fusion Configuration
C.fusion_config = {
    'use_gccm': True,  # 使用GCCM进行三主干特征融合
    'gccm_window_size': 8,  # GCCM窗口大小
    'gccm_num_heads': 16,   # GCCM注意力头数
    'gccm_mlp_ratio': 4.0,  # GCCM MLP比率
    'ffm_reduction': 1,     # FFM降维比率
}

"""Train Config"""
# 三主干网络需要更小的学习率和更长的预热
C.lr = 8e-6  # 进一步降低学习率，适应三主干网络的复杂性
C.lr_power = 0.9
C.momentum = 0.9
C.weight_decay = 0.01
C.grad_clip = 1.0  # 添加梯度裁剪阈值
C.batch_size = 8
C.nepochs = 300
C.niters_per_epoch = C.num_train_imgs // C.batch_size  + 1
C.num_workers = 0  # Windows系统下设置为0，避免多进程问题
C.train_scale_array = [0.5, 0.75, 1, 1.25, 1.5, 1.75]
C.warm_up_epoch = 30  # 增加预热轮数，使三主干网络训练更加稳定

# 日志配置
C.log_loss_components = True  # 是否记录损失组件详情
C.log_frequency = 'epoch'     # 'batch' 或 'epoch' - 控制详细日志的频率
C.log_triple_backbone = True  # 是否记录三主干网络的详细信息
C.log_fusion_details = True   # 是否记录特征融合的详细信息

C.fix_bias = True
C.bn_eps = 1e-3
C.bn_momentum = 0.1

"""Loss Function Config"""
# 简化损失函数组合：CE + Focal
C.loss_type = 'combined'
C.ce_weight = 1.0           # 交叉熵权重
C.focal_weight = 0.0        # Focal权重
C.dice_weight = 0.0         # Dice权重
C.lovasz_weight = 0.0       # Lovasz权重
C.boundary_weight = 0.0     # 边界损失权重
C.context_weight = 0.0      # 上下文损失权重

# 损失函数权重字典
C.loss_weights = {
    'ce_weight': C.ce_weight,
    'dice_weight': C.dice_weight,
    'focal_weight': C.focal_weight,
    'lovasz_weight': C.lovasz_weight,
    'boundary_weight': C.boundary_weight,
    'context_weight': C.context_weight
}

# Focal Loss 参数
C.focal_gamma = 2.0         # Focal Loss gamma参数
C.focal_alpha = 0.25        # Focal Loss alpha参数

# 其他损失函数参数
C.label_smoothing = 0.05    # 标签平滑参数

# 类别权重配置
C.use_class_weights = False
C.class_weights = None


"""Eval Config"""
C.eval_iter = 25
C.eval_stride_rate = 2 / 3
C.eval_scale_array = [1]  # 使用单一尺度进行评估
C.eval_flip = False  # 不使用翻转增强
C.eval_crop_size = [256, 256]  # 调整为与训练图像尺寸一致
C.eval_batch_size = 12  # 验证时的batch size，比训练时更大
C.eval_frequency = 5  # 每5轮进行一次验证

"""Store Config"""
C.checkpoint_start_epoch = 1  # 从第一轮开始保存检查点
C.checkpoint_step = 5  # 每5轮保存一次检查点

"""Resume Config"""
C.resume_from = None  # 从指定检查点继续训练
C.resume_model = None  # 兼容旧版本代码

"""Path Config"""
def add_path(path):
    if path not in sys.path:
        sys.path.insert(0, path)
add_path(osp.join(C.root_dir))

C.log_dir = osp.abspath('log_' + C.dataset_name + '_' + C.backbone + '_triple')
C.tb_dir = osp.abspath(osp.join(C.log_dir, "tb"))
C.log_dir_link = C.log_dir
C.checkpoint_dir = osp.abspath(osp.join(C.log_dir, "checkpoint"))

exp_time = time.strftime('%Y_%m_%d_%H_%M_%S', time.localtime())
C.log_file = C.log_dir + '/log_' + exp_time + '.log'
C.link_log_file = C.log_file + '/log_last.log'
C.val_log_file = C.log_dir + '/val_' + exp_time + '.log'
C.link_val_log_file = C.log_dir + '/val_last.log'



if __name__ == '__main__':
    print(f"训练轮数: {C.nepochs}")