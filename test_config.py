#!/usr/bin/env python3
"""
测试脚本：验证三主干神经网络的配置
"""

import torch
import torch.nn as nn
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import config
from models.builder import EncoderDecoder as segmodel


def test_config():
    """测试配置文件"""
    print("=" * 60)
    print("测试三主干神经网络配置")
    print("=" * 60)
    
    print(f"数据集: {config.dataset_name}")
    print(f"主干网络: {config.backbone}")
    print(f"解码器: {config.decoder}")
    print(f"使用三主干架构: {config.use_triple_backbone}")
    print(f"批处理大小: {config.batch_size}")
    print(f"学习率: {config.lr}")
    print(f"预热轮数: {config.warm_up_epoch}")
    print(f"日志目录: {config.log_dir}")
    
    print("\nSFS主干配置:")
    for key, value in config.sfs_backbone_config.items():
        print(f"  {key}: {value}")
    
    print("\n特征融合配置:")
    for key, value in config.fusion_config.items():
        print(f"  {key}: {value}")
    
    print("\n损失函数配置:")
    print(f"  损失类型: {config.loss_type}")
    print(f"  CE权重: {config.ce_weight}")
    print(f"  Focal权重: {config.focal_weight}")
    print(f"  Dice权重: {config.dice_weight}")
    print(f"  Lovasz权重: {config.lovasz_weight}")


def test_model_creation():
    """测试模型创建"""
    print("\n" + "=" * 60)
    print("测试三主干模型创建")
    print("=" * 60)
    
    try:
        # 创建损失函数
        criterion = nn.CrossEntropyLoss(
            reduction='mean',
            ignore_index=config.background
        )
        
        # 创建模型
        print("创建三主干模型...")
        model = segmodel(cfg=config, criterion=criterion, norm_layer=nn.BatchNorm2d)
        print("✓ 模型创建成功！")
        
        # 打印模型信息
        total_params = sum(p.numel() for p in model.parameters())
        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        
        print(f"总参数数量: {total_params:,}")
        print(f"可训练参数数量: {trainable_params:,}")
        
        # 测试前向传播
        print("\n测试前向传播...")
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        model.to(device)
        
        # 创建测试输入
        batch_size = 2
        rgb_input = torch.randn(batch_size, 3, config.image_height, config.image_width).to(device)
        modal_input = torch.randn(batch_size, 3, config.image_height, config.image_width).to(device)
        
        print(f"RGB输入形状: {rgb_input.shape}")
        print(f"模态输入形状: {modal_input.shape}")
        
        # 前向传播
        with torch.no_grad():
            output = model.encode_decode(rgb_input, modal_input)
            
        if isinstance(output, tuple):
            main_output, aux_output = output
            print(f"主输出形状: {main_output.shape}")
            print(f"辅助输出形状: {aux_output.shape}")
        else:
            print(f"输出形状: {output.shape}")
        
        print("✓ 前向传播测试成功！")
        
        return model
        
    except Exception as e:
        print(f"❌ 模型创建失败: {e}")
        import traceback
        traceback.print_exc()
        return None


def test_training_compatibility():
    """测试训练兼容性"""
    print("\n" + "=" * 60)
    print("测试训练兼容性")
    print("=" * 60)
    
    try:
        # 检查必要的配置项
        required_configs = [
            'dataset_name', 'backbone', 'decoder', 'num_classes',
            'lr', 'batch_size', 'nepochs', 'optimizer',
            'loss_type', 'ce_weight', 'focal_weight'
        ]
        
        missing_configs = []
        for cfg_name in required_configs:
            if not hasattr(config, cfg_name):
                missing_configs.append(cfg_name)
        
        if missing_configs:
            print(f"❌ 缺少必要配置项: {missing_configs}")
            return False
        
        print("✓ 所有必要配置项都存在")
        
        # 检查三主干特定配置
        triple_configs = ['use_triple_backbone', 'sfs_backbone_config', 'fusion_config']
        for cfg_name in triple_configs:
            if hasattr(config, cfg_name):
                print(f"✓ 三主干配置 {cfg_name} 存在")
            else:
                print(f"⚠ 三主干配置 {cfg_name} 不存在")
        
        # 检查路径配置
        print(f"\n路径配置检查:")
        print(f"  数据集路径: {config.dataset_path}")
        print(f"  RGB路径: {config.rgb_root_folder}")
        print(f"  模态X路径: {config.x_root_folder}")
        print(f"  标签路径: {config.gt_root_folder}")
        print(f"  日志目录: {config.log_dir}")
        
        print("✓ 训练兼容性检查通过！")
        return True
        
    except Exception as e:
        print(f"❌ 训练兼容性检查失败: {e}")
        return False


def main():
    """主函数"""
    print("开始测试三主干神经网络配置...")
    
    # 测试配置
    test_config()
    
    # 测试模型创建
    model = test_model_creation()
    
    # 测试训练兼容性
    compatibility_ok = test_training_compatibility()
    
    print("\n" + "=" * 60)
    if model is not None and compatibility_ok:
        print("✓ 所有测试通过！")
        print("✓ 三主干神经网络配置正确！")
        print("✓ 可以开始训练！")
    else:
        print("❌ 部分测试失败，请检查配置")
    print("=" * 60)


if __name__ == "__main__":
    main()
