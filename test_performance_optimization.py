"""
性能优化测试脚本
测试优化前后的训练速度差异
"""

import torch
import torch.nn as nn
import time
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.abspath('.'))

from config import config as cfg
from models.encoders.dual_swin import TripleSwinTransformer
from models.encoders.overlock_backbone import OverLoCKBackbone


def benchmark_model(model, input_rgb, input_sar, num_iterations=10, warmup_iterations=3):
    """
    基准测试模型性能
    """
    model.train()
    
    # 预热
    print("预热中...")
    for _ in range(warmup_iterations):
        with torch.no_grad():
            _ = model(input_rgb, input_sar)
    
    # 清理GPU缓存
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        torch.cuda.synchronize()
    
    # 正式测试
    print(f"开始性能测试 ({num_iterations} 次迭代)...")
    
    # Forward pass测试
    forward_times = []
    for i in range(num_iterations):
        if torch.cuda.is_available():
            torch.cuda.synchronize()
        
        start_time = time.time()
        
        with torch.no_grad():
            outputs = model(input_rgb, input_sar)
        
        if torch.cuda.is_available():
            torch.cuda.synchronize()
        
        end_time = time.time()
        forward_time = end_time - start_time
        forward_times.append(forward_time)
        
        print(f"迭代 {i+1}/{num_iterations}: {forward_time:.4f}s")
    
    # Backward pass测试
    print("\n测试反向传播...")
    backward_times = []
    
    for i in range(num_iterations):
        if torch.cuda.is_available():
            torch.cuda.synchronize()
        
        start_time = time.time()
        
        # Forward
        outputs = model(input_rgb, input_sar)
        loss = sum(output.sum() for output in outputs)
        
        # Backward
        loss.backward()
        
        if torch.cuda.is_available():
            torch.cuda.synchronize()
        
        end_time = time.time()
        backward_time = end_time - start_time
        backward_times.append(backward_time)
        
        # 清理梯度
        model.zero_grad()
        
        print(f"迭代 {i+1}/{num_iterations}: {backward_time:.4f}s")
    
    return forward_times, backward_times


def test_overlock_backbone_performance():
    """测试OverLoCK主干的性能"""
    print("\n" + "="*60)
    print("测试 OverLoCK Backbone 性能")
    print("="*60)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 创建OverLoCK主干（不加载预训练权重以避免尺寸不匹配）
    overlock_config = cfg.overlock_backbone_config.copy()
    overlock_config.pop('pretrained_path', None)  # 移除预训练权重路径

    overlock_backbone = OverLoCKBackbone(
        **overlock_config
    ).to(device)
    
    # 测试输入
    batch_size = 4
    input_tensor = torch.randn(batch_size, 3, 256, 256).to(device)
    
    print(f"输入形状: {input_tensor.shape}")
    print(f"模型参数数量: {sum(p.numel() for p in overlock_backbone.parameters()):,}")
    
    # 性能测试 - OverLoCK只需要一个输入
    def benchmark_overlock(model, input_tensor, num_iterations=5):
        model.train()

        # 预热
        print("预热中...")
        for _ in range(3):
            with torch.no_grad():
                _ = model(input_tensor)

        # 清理GPU缓存
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
            torch.cuda.synchronize()

        # Forward pass测试
        forward_times = []
        for i in range(num_iterations):
            if torch.cuda.is_available():
                torch.cuda.synchronize()

            start_time = time.time()

            with torch.no_grad():
                outputs = model(input_tensor)

            if torch.cuda.is_available():
                torch.cuda.synchronize()

            end_time = time.time()
            forward_time = end_time - start_time
            forward_times.append(forward_time)

            print(f"迭代 {i+1}/{num_iterations}: {forward_time:.4f}s")

        # Backward pass测试
        print("\n测试反向传播...")
        backward_times = []

        for i in range(num_iterations):
            if torch.cuda.is_available():
                torch.cuda.synchronize()

            start_time = time.time()

            # Forward
            outputs = model(input_tensor)
            loss = sum(output.sum() for output in outputs)

            # Backward
            loss.backward()

            if torch.cuda.is_available():
                torch.cuda.synchronize()

            end_time = time.time()
            backward_time = end_time - start_time
            backward_times.append(backward_time)

            # 清理梯度
            model.zero_grad()

            print(f"迭代 {i+1}/{num_iterations}: {backward_time:.4f}s")

        return forward_times, backward_times

    forward_times, backward_times = benchmark_overlock(overlock_backbone, input_tensor, 5)
    
    # 统计结果
    avg_forward = sum(forward_times) / len(forward_times)
    avg_backward = sum(backward_times) / len(backward_times)
    
    print(f"\n性能统计:")
    print(f"平均前向传播时间: {avg_forward:.4f}s")
    print(f"平均反向传播时间: {avg_backward:.4f}s")
    print(f"平均总时间: {avg_forward + avg_backward:.4f}s")
    print(f"预估吞吐量: {batch_size / (avg_forward + avg_backward):.2f} samples/s")
    
    return avg_forward, avg_backward


def test_triple_swin_performance():
    """测试三主干网络的性能"""
    print("\n" + "="*60)
    print("测试 Triple Swin Transformer 性能")
    print("="*60)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 创建三主干模型（不加载预训练权重）
    overlock_config = cfg.overlock_backbone_config.copy()
    overlock_config.pop('pretrained_path', None)  # 移除预训练权重路径

    model = TripleSwinTransformer(
        pretrain_img_size=224,
        patch_size=4,
        in_chans=3,
        embed_dim=96,
        depths=[2, 2, 6, 2],
        num_heads=[3, 6, 12, 24],
        window_size=7,
        out_indices=(0, 1, 2, 3),
        overlock_config=overlock_config
    ).to(device)
    
    # 测试输入
    batch_size = 2  # 三主干网络使用较小的batch size
    input_rgb = torch.randn(batch_size, 3, 256, 256).to(device)
    input_sar = torch.randn(batch_size, 3, 256, 256).to(device)
    
    print(f"RGB输入形状: {input_rgb.shape}")
    print(f"SAR输入形状: {input_sar.shape}")
    print(f"模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # 性能测试
    forward_times, backward_times = benchmark_model(
        model, input_rgb, input_sar, num_iterations=3
    )
    
    # 统计结果
    avg_forward = sum(forward_times) / len(forward_times)
    avg_backward = sum(backward_times) / len(backward_times)
    
    print(f"\n性能统计:")
    print(f"平均前向传播时间: {avg_forward:.4f}s")
    print(f"平均反向传播时间: {avg_backward:.4f}s")
    print(f"平均总时间: {avg_forward + avg_backward:.4f}s")
    print(f"预估吞吐量: {batch_size / (avg_forward + avg_backward):.2f} samples/s")
    
    return avg_forward, avg_backward


def test_memory_usage():
    """测试内存使用情况"""
    print("\n" + "="*60)
    print("测试内存使用情况")
    print("="*60)
    
    if not torch.cuda.is_available():
        print("CUDA不可用，跳过内存测试")
        return
    
    device = torch.device('cuda')
    
    # 清理GPU缓存
    torch.cuda.empty_cache()
    initial_memory = torch.cuda.memory_allocated(device)
    
    print(f"初始GPU内存使用: {initial_memory / 1024**2:.2f} MB")
    
    # 创建模型（不加载预训练权重）
    overlock_config = cfg.overlock_backbone_config.copy()
    overlock_config.pop('pretrained_path', None)  # 移除预训练权重路径

    model = TripleSwinTransformer(
        pretrain_img_size=224,
        patch_size=4,
        in_chans=3,
        embed_dim=96,
        depths=[2, 2, 6, 2],
        num_heads=[3, 6, 12, 24],
        window_size=7,
        out_indices=(0, 1, 2, 3),
        overlock_config=overlock_config
    ).to(device)
    
    model_memory = torch.cuda.memory_allocated(device)
    print(f"模型加载后GPU内存使用: {model_memory / 1024**2:.2f} MB")
    print(f"模型占用内存: {(model_memory - initial_memory) / 1024**2:.2f} MB")
    
    # 测试不同batch size的内存使用
    batch_sizes = [1, 2, 4, 8]
    
    for batch_size in batch_sizes:
        try:
            torch.cuda.empty_cache()
            
            input_rgb = torch.randn(batch_size, 3, 256, 256).to(device)
            input_sar = torch.randn(batch_size, 3, 256, 256).to(device)
            
            # Forward pass
            outputs = model(input_rgb, input_sar)
            loss = sum(output.sum() for output in outputs)
            
            forward_memory = torch.cuda.memory_allocated(device)
            
            # Backward pass
            loss.backward()
            
            backward_memory = torch.cuda.memory_allocated(device)
            
            print(f"Batch Size {batch_size}:")
            print(f"  前向传播内存: {forward_memory / 1024**2:.2f} MB")
            print(f"  反向传播内存: {backward_memory / 1024**2:.2f} MB")
            print(f"  峰值内存: {torch.cuda.max_memory_allocated(device) / 1024**2:.2f} MB")
            
            # 清理
            del input_rgb, input_sar, outputs, loss
            model.zero_grad()
            torch.cuda.reset_peak_memory_stats(device)
            
        except RuntimeError as e:
            if "out of memory" in str(e):
                print(f"Batch Size {batch_size}: GPU内存不足")
                break
            else:
                raise e


def main():
    """主测试函数"""
    print("开始性能优化测试...")
    print(f"PyTorch版本: {torch.__version__}")
    print(f"CUDA可用: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"CUDA版本: {torch.version.cuda}")
        print(f"GPU设备: {torch.cuda.get_device_name()}")
        print(f"GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.2f} GB")
    
    try:
        # 测试OverLoCK主干性能
        overlock_forward, overlock_backward = test_overlock_backbone_performance()
        
        # 测试三主干网络性能
        triple_forward, triple_backward = test_triple_swin_performance()
        
        # 测试内存使用
        test_memory_usage()
        
        # 总结
        print("\n" + "="*60)
        print("性能测试总结")
        print("="*60)
        print(f"OverLoCK主干 - 前向: {overlock_forward:.4f}s, 反向: {overlock_backward:.4f}s")
        print(f"三主干网络 - 前向: {triple_forward:.4f}s, 反向: {triple_backward:.4f}s")
        print(f"总体训练时间估计: {triple_forward + triple_backward:.4f}s/batch")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
