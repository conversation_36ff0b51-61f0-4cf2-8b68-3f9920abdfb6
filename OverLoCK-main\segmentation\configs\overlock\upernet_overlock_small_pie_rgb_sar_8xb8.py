_base_ = [
    '../_base_/models/upernet_overlock_s.py',
    '../_base_/datasets/pie_rgb_sar.py',
    '../_base_/default_runtime.py',
    '../_base_/schedules/schedule_80k.py'
]

# model settings
model = dict(
    pretrained=None,
    backbone=dict(
        _delete_=True,
        type='overlock_s',
        pretrained=False,  # 不使用自动下载
        drop_path_rate=0.3,
    ),
    decode_head=dict(
        in_index=[0, 1, 2, 3],
        in_channels=[64, 128, 320, 512],
        num_classes=4,
    ),
    auxiliary_head=dict(
        in_index=2,
        in_channels=320,
        num_classes=4
    ),
)

# optimizer
optimizer = dict(_delete_=True, type='AdamW', lr=6e-5, betas=(0.9, 0.999), weight_decay=0.01,
                 paramwise_cfg=dict(custom_keys={'absolute_pos_embed': dict(decay_mult=0.),
                                                 'relative_position_bias_table': dict(decay_mult=0.),
                                                 'norm': dict(decay_mult=0.)}))

lr_config = dict(_delete_=True, policy='poly',
                 warmup='linear',
                 warmup_iters=1500,
                 warmup_ratio=1e-6,
                 power=1.0, min_lr=0.0, by_epoch=False)

# pretrained weights
load_from = 'overlock/overlock_s_in1k_224.pth'

# runtime settings
runner = dict(type='IterBasedRunner', max_iters=80000)
checkpoint_config = dict(by_epoch=False, interval=1000)
evaluation = dict(interval=8000, metric='mIoU', pre_eval=True)

# dataset settings
data = dict(
    samples_per_gpu=8,
    workers_per_gpu=4,
)

# logging
log_config = dict(
    interval=50,
    hooks=[
        dict(type='TextLoggerHook', by_epoch=False),
        dict(type='TensorboardLoggerHook')
    ])

# misc
find_unused_parameters = True
