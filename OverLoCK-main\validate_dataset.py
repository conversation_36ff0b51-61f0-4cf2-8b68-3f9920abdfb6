#!/usr/bin/env python3
"""
验证PIE-RGB-SAR数据集配置是否正确
"""

import os
import sys
import numpy as np
from PIL import Image
import matplotlib.pyplot as plt

def check_dataset_structure():
    """检查数据集目录结构"""
    print("=== 检查数据集目录结构 ===")
    
    base_dir = "datasets/PIE-RGB-SAR"
    required_dirs = ["RGB", "Label"]
    required_files = ["train.txt", "val.txt"]
    
    if not os.path.exists(base_dir):
        print(f"❌ 数据集目录不存在: {base_dir}")
        return False
    
    for dir_name in required_dirs:
        dir_path = os.path.join(base_dir, dir_name)
        if not os.path.exists(dir_path):
            print(f"❌ 子目录不存在: {dir_path}")
            return False
        else:
            file_count = len([f for f in os.listdir(dir_path) if f.endswith('.tif')])
            print(f"✅ {dir_name} 目录存在，包含 {file_count} 个 .tif 文件")
    
    for file_name in required_files:
        file_path = os.path.join(base_dir, file_name)
        if not os.path.exists(file_path):
            print(f"❌ 文件不存在: {file_path}")
            return False
        else:
            with open(file_path, 'r') as f:
                line_count = len(f.readlines())
            print(f"✅ {file_name} 存在，包含 {line_count} 行")
    
    return True

def check_data_consistency():
    """检查数据一致性"""
    print("\n=== 检查数据一致性 ===")
    
    base_dir = "datasets/PIE-RGB-SAR"
    
    # 读取训练和验证文件列表
    train_files = []
    val_files = []
    
    with open(os.path.join(base_dir, "train.txt"), 'r') as f:
        train_files = [line.strip() for line in f.readlines()]
    
    with open(os.path.join(base_dir, "val.txt"), 'r') as f:
        val_files = [line.strip() for line in f.readlines()]
    
    print(f"训练集样本数: {len(train_files)}")
    print(f"验证集样本数: {len(val_files)}")
    
    # 检查文件是否存在
    missing_rgb = []
    missing_label = []
    
    all_files = train_files + val_files
    for i, filename in enumerate(all_files[:10]):  # 只检查前10个文件
        rgb_path = os.path.join(base_dir, "RGB", f"{filename}.tif")
        label_path = os.path.join(base_dir, "Label", f"{filename}.tif")
        
        if not os.path.exists(rgb_path):
            missing_rgb.append(filename)
        if not os.path.exists(label_path):
            missing_label.append(filename)
    
    if missing_rgb:
        print(f"❌ 缺失RGB文件: {len(missing_rgb)} 个")
    else:
        print("✅ RGB文件检查通过")
    
    if missing_label:
        print(f"❌ 缺失标签文件: {len(missing_label)} 个")
    else:
        print("✅ 标签文件检查通过")
    
    return len(missing_rgb) == 0 and len(missing_label) == 0

def analyze_sample_data():
    """分析样本数据"""
    print("\n=== 分析样本数据 ===")
    
    base_dir = "datasets/PIE-RGB-SAR"
    
    # 读取第一个训练样本
    with open(os.path.join(base_dir, "train.txt"), 'r') as f:
        first_sample = f.readline().strip()
    
    rgb_path = os.path.join(base_dir, "RGB", f"{first_sample}.tif")
    label_path = os.path.join(base_dir, "Label", f"{first_sample}.tif")
    
    if os.path.exists(rgb_path) and os.path.exists(label_path):
        # 读取RGB图像
        rgb_img = np.array(Image.open(rgb_path))
        print(f"RGB图像形状: {rgb_img.shape}")
        print(f"RGB图像数据类型: {rgb_img.dtype}")
        print(f"RGB图像值范围: {rgb_img.min()} - {rgb_img.max()}")
        
        # 读取标签图像
        label_img = np.array(Image.open(label_path))
        print(f"标签图像形状: {label_img.shape}")
        print(f"标签图像数据类型: {label_img.dtype}")
        print(f"标签图像值范围: {label_img.min()} - {label_img.max()}")
        
        # 分析标签分布
        unique_labels, counts = np.unique(label_img, return_counts=True)
        print(f"标签类别: {unique_labels}")
        print("各类别像素数量:")
        for label, count in zip(unique_labels, counts):
            percentage = count / label_img.size * 100
            print(f"  类别 {label}: {count} 像素 ({percentage:.2f}%)")
        
        return True
    else:
        print(f"❌ 无法读取样本文件: {first_sample}")
        return False

def visualize_sample():
    """可视化样本"""
    print("\n=== 可视化样本 ===")
    
    base_dir = "datasets/PIE-RGB-SAR"
    
    # 读取第一个训练样本
    with open(os.path.join(base_dir, "train.txt"), 'r') as f:
        first_sample = f.readline().strip()
    
    rgb_path = os.path.join(base_dir, "RGB", f"{first_sample}.tif")
    label_path = os.path.join(base_dir, "Label", f"{first_sample}.tif")
    
    if os.path.exists(rgb_path) and os.path.exists(label_path):
        rgb_img = np.array(Image.open(rgb_path))
        label_img = np.array(Image.open(label_path))
        
        # 创建可视化
        fig, axes = plt.subplots(1, 2, figsize=(12, 6))
        
        # 显示RGB图像
        if len(rgb_img.shape) == 3:
            axes[0].imshow(rgb_img)
        else:
            axes[0].imshow(rgb_img, cmap='gray')
        axes[0].set_title('RGB Image')
        axes[0].axis('off')
        
        # 显示标签图像
        axes[1].imshow(label_img, cmap='viridis')
        axes[1].set_title('Label Image')
        axes[1].axis('off')
        
        plt.tight_layout()
        plt.savefig('sample_visualization.png', dpi=150, bbox_inches='tight')
        print("✅ 样本可视化已保存为 sample_visualization.png")
        
        return True
    else:
        print(f"❌ 无法读取样本文件进行可视化")
        return False

def main():
    print("PIE-RGB-SAR 数据集验证工具")
    print("=" * 50)
    
    success = True
    
    # 检查目录结构
    success &= check_dataset_structure()
    
    # 检查数据一致性
    success &= check_data_consistency()
    
    # 分析样本数据
    success &= analyze_sample_data()
    
    # 可视化样本
    try:
        visualize_sample()
    except Exception as e:
        print(f"⚠️ 可视化失败: {e}")
    
    print("\n" + "=" * 50)
    if success:
        print("✅ 数据集验证通过！可以开始训练。")
    else:
        print("❌ 数据集验证失败！请检查数据集配置。")
    
    return success

if __name__ == '__main__':
    main()
