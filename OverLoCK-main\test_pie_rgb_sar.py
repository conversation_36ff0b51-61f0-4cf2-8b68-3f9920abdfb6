#!/usr/bin/env python3
"""
测试PIE-RGB-SAR数据集的语义分割模型
"""

import os
import sys
import argparse
import subprocess

def main():
    parser = argparse.ArgumentParser(description='Test PIE-RGB-SAR semantic segmentation model')
    parser.add_argument('--config', 
                        default='segmentation/configs/overlock/upernet_overlock_small_pie_rgb_sar_8xb8.py',
                        help='test config file path')
    parser.add_argument('--checkpoint', 
                        required=True,
                        help='checkpoint file')
    parser.add_argument('--out', 
                        help='output result file in pickle format')
    parser.add_argument('--eval', 
                        default='mIoU',
                        help='evaluation metrics')
    parser.add_argument('--show', action='store_true',
                        help='show results')
    parser.add_argument('--show-dir', 
                        help='directory where painted images will be saved')
    parser.add_argument('--opacity', type=float, default=0.5,
                        help='opacity of painted segmentation map')
    
    args = parser.parse_args()
    
    # 构建测试命令
    cmd = [
        'python', 'segmentation/test.py',
        args.config,
        args.checkpoint,
        '--eval', args.eval,
    ]
    
    if args.out:
        cmd.extend(['--out', args.out])
    
    if args.show:
        cmd.append('--show')
    
    if args.show_dir:
        cmd.extend(['--show-dir', args.show_dir])
        cmd.extend(['--opacity', str(args.opacity)])
    
    print(f"执行命令: {' '.join(cmd)}")
    
    # 执行测试
    try:
        subprocess.run(cmd, check=True)
    except subprocess.CalledProcessError as e:
        print(f"测试失败: {e}")
        sys.exit(1)
    except KeyboardInterrupt:
        print("测试被用户中断")
        sys.exit(1)

if __name__ == '__main__':
    main()
