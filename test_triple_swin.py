#!/usr/bin/env python3
"""
测试脚本：验证三主干神经网络的实现
"""

import torch
import torch.nn as nn
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models.encoders.dual_swin import TripleSwinTransformer, swin_s
from models.encoders.SFS_backbone import SFSBackbone


def test_sfs_backbone():
    """测试 SFS 主干网络"""
    print("=" * 50)
    print("测试 SFS Backbone")
    print("=" * 50)
    
    # 创建 SFS backbone
    sfs_backbone = SFSBackbone(
        patch_size=4,
        in_chans=3,
        embed_dim=96,
        depths=[2, 2, 6, 2],
        out_indices=(0, 1, 2, 3)
    )
    
    # 测试输入
    x = torch.randn(2, 3, 224, 224)
    
    print(f"输入形状: {x.shape}")
    
    # 前向传播
    with torch.no_grad():
        outputs = sfs_backbone(x)
    
    print(f"输出阶段数: {len(outputs)}")
    for i, out in enumerate(outputs):
        print(f"阶段 {i} 输出形状: {out.shape}")
    
    return outputs


def test_triple_swin():
    """测试三主干 Swin Transformer"""
    print("\n" + "=" * 50)
    print("测试 Triple Swin Transformer")
    print("=" * 50)
    
    # 创建三主干模型
    model = TripleSwinTransformer(
        pretrain_img_size=224,
        patch_size=4,
        in_chans=3,
        embed_dim=96,
        depths=[2, 2, 6, 2],
        num_heads=[3, 6, 12, 24],
        window_size=7,
        out_indices=(0, 1, 2, 3)
    )
    
    # 测试输入
    x_rgb = torch.randn(2, 3, 224, 224)
    x_depth = torch.randn(2, 3, 224, 224)
    
    print(f"RGB 输入形状: {x_rgb.shape}")
    print(f"Depth 输入形状: {x_depth.shape}")
    
    # 前向传播
    with torch.no_grad():
        outputs = model(x_rgb, x_depth)
    
    print(f"融合后输出阶段数: {len(outputs)}")
    for i, out in enumerate(outputs):
        print(f"阶段 {i} 融合输出形状: {out.shape}")
    
    return outputs


def test_swin_s():
    """测试 swin_s 模型"""
    print("\n" + "=" * 50)
    print("测试 swin_s 模型")
    print("=" * 50)
    
    # 创建 swin_s 模型
    model = swin_s()
    
    # 测试输入
    x_rgb = torch.randn(1, 3, 224, 224)
    x_depth = torch.randn(1, 3, 224, 224)
    
    print(f"RGB 输入形状: {x_rgb.shape}")
    print(f"Depth 输入形状: {x_depth.shape}")
    
    # 前向传播
    with torch.no_grad():
        outputs = model(x_rgb, x_depth)
    
    print(f"swin_s 输出阶段数: {len(outputs)}")
    for i, out in enumerate(outputs):
        print(f"阶段 {i} 输出形状: {out.shape}")
    
    return outputs


def compare_shapes():
    """比较不同模型的输出形状"""
    print("\n" + "=" * 50)
    print("形状对比分析")
    print("=" * 50)
    
    # SFS backbone 输出
    sfs_outputs = test_sfs_backbone()
    
    # Triple Swin 输出
    triple_outputs = test_triple_swin()
    
    print("\n形状对比:")
    print("-" * 30)
    for i in range(len(sfs_outputs)):
        print(f"阶段 {i}:")
        print(f"  SFS Backbone: {sfs_outputs[i].shape}")
        print(f"  Triple Swin:  {triple_outputs[i].shape}")
        
        # 检查形状是否匹配
        if sfs_outputs[i].shape == triple_outputs[i].shape:
            print(f"  ✓ 形状匹配")
        else:
            print(f"  ✗ 形状不匹配")
        print()


if __name__ == "__main__":
    print("开始测试三主干神经网络实现...")
    
    try:
        # 运行所有测试
        compare_shapes()
        test_swin_s()
        
        print("\n" + "=" * 50)
        print("✓ 所有测试完成！")
        print("✓ 三主干神经网络实现成功！")
        print("=" * 50)
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
