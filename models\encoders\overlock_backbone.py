"""
OverLoCK Backbone Adapter for Triple Backbone Architecture

This module adapts the OverLoCK network to work as a third backbone in the triple backbone architecture,
maintaining the same output tensor shapes as the SFS backbone.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import sys
import os

# Import our modified OverLoCK implementation
from .overlock_components import OverLoCK


class OverLoCKBackbone(nn.Module):
    """
    OverLoCK Backbone Adapter for Triple Backbone Architecture
    
    This class adapts the OverLoCK network to work as a third backbone,
    providing the same interface and output shapes as the SFS backbone.
    """
    
    def __init__(self,
                 depth=[6, 6, 8, 3],
                 sub_depth=[16, 3],
                 in_chans=3,
                 embed_dim=[64, 128, 320, 512],
                 kernel_size=[17, 15, 13, 7],
                 mlp_ratio=[4, 4, 4, 4],
                 sub_num_heads=[8, 16],
                 sub_mlp_ratio=[3, 3],
                 ls_init_value=[None, None, 1, 1],
                 res_scale=True,
                 smk_size=5,
                 deploy=False,
                 use_gemm=True,
                 use_ds=False,
                 drop_rate=0,
                 drop_path_rate=0.2,
                 num_classes=0,
                 use_checkpoint=[0, 0, 0, 0],
                 out_indices=(0, 1, 2, 3),
                 pretrained_path=None):
        super().__init__()
        
        self.out_indices = out_indices
        self.embed_dim = embed_dim
        self.num_layers = len(embed_dim)
        
        # Create OverLoCK model
        self.overlock = OverLoCK(
            depth=depth,
            sub_depth=sub_depth,
            in_chans=in_chans,
            embed_dim=embed_dim,
            kernel_size=kernel_size,
            mlp_ratio=mlp_ratio,
            sub_num_heads=sub_num_heads,
            sub_mlp_ratio=sub_mlp_ratio,
            ls_init_value=ls_init_value,
            res_scale=res_scale,
            smk_size=smk_size,
            deploy=deploy,
            use_gemm=use_gemm,
            use_ds=use_ds,
            drop_rate=drop_rate,
            drop_path_rate=drop_path_rate,
            num_classes=num_classes,
            use_checkpoint=use_checkpoint
        )
        
        # Load pretrained weights if provided
        if pretrained_path and os.path.exists(pretrained_path):
            self.load_pretrained_weights(pretrained_path)
        
        # Feature projection layers to match expected output dimensions
        # These layers ensure the output channels match the Swin Transformer dimensions
        self.feature_projections = nn.ModuleList()
        swin_dims = [96, 192, 384, 768]  # Standard Swin Transformer dimensions

        # Actual OverLoCK output dimensions based on the test results
        actual_overlock_dims = [64, 128, 320, 128]  # Stage 3 outputs 128 instead of 512

        for i, (overlock_dim, swin_dim) in enumerate(zip(actual_overlock_dims, swin_dims)):
            if overlock_dim != swin_dim:
                proj = nn.Sequential(
                    nn.Conv2d(overlock_dim, swin_dim, kernel_size=1, bias=False),
                    nn.BatchNorm2d(swin_dim),
                    nn.GELU()
                )
            else:
                proj = nn.Identity()
            self.feature_projections.append(proj)
    
    def load_pretrained_weights(self, pretrained_path):
        """Load pretrained weights from OverLoCK checkpoint"""
        try:
            print(f"Loading OverLoCK pretrained weights from: {pretrained_path}")
            checkpoint = torch.load(pretrained_path, map_location='cpu')
            
            # Handle different checkpoint formats
            if 'model' in checkpoint:
                state_dict = checkpoint['model']
            elif 'state_dict' in checkpoint:
                state_dict = checkpoint['state_dict']
            else:
                state_dict = checkpoint
            
            # Remove 'head' and 'aux_head' layers since we don't need classification
            filtered_state_dict = {}
            for k, v in state_dict.items():
                if not k.startswith(('head.', 'aux_head.')):
                    filtered_state_dict[k] = v
            
            # Load weights with strict=False to ignore missing keys
            missing_keys, unexpected_keys = self.overlock.load_state_dict(filtered_state_dict, strict=False)
            
            if missing_keys:
                print(f"Missing keys: {missing_keys}")
            if unexpected_keys:
                print(f"Unexpected keys: {unexpected_keys}")
                
            print("OverLoCK pretrained weights loaded successfully!")
            
        except Exception as e:
            print(f"Warning: Failed to load pretrained weights: {e}")
            print("Continuing with random initialization...")
    
    def extract_stage_features(self, x):
        """
        Extract features from each stage of OverLoCK network
        Returns features at different scales similar to SFS backbone

        OverLoCK stages:
        - Stage 0: patch_embed1 + blocks1 -> [B, 64, H/4, W/4]
        - Stage 1: patch_embed2 + blocks2 -> [B, 128, H/8, W/8]
        - Stage 2: patch_embed3 + blocks3 -> [B, 320, H/16, W/16]
        - Stage 3: patch_embed4 + blocks4 + sub_blocks -> [B, 512, H/32, W/32]
        """
        features = []

        # Stage 0: patch_embed1 + blocks1
        x = self.overlock.patch_embed1(x)  # [B, 64, H/4, W/4]
        for blk in self.overlock.blocks1:
            x = blk(x)
        features.append(x)  # Stage 0 output

        # Stage 1: patch_embed2 + blocks2
        x = self.overlock.patch_embed2(x)  # [B, 128, H/8, W/8]
        for blk in self.overlock.blocks2:
            x = blk(x)
        features.append(x)  # Stage 1 output

        # Stage 2: patch_embed3 + blocks3
        x = self.overlock.patch_embed3(x)  # [B, 320, H/16, W/16]
        for blk in self.overlock.blocks3:
            x = blk(x)
        features.append(x)  # Stage 2 output

        # Stage 3: patch_embed4 + blocks4 + sub_blocks
        ctx = self.overlock.patch_embed4(x)  # [B, 512, H/32, W/32]
        for blk in self.overlock.blocks4:
            ctx = blk(ctx)

        # Apply sub_blocks for final refinement
        ctx_cls = ctx
        ctx_ori = self.overlock.high_level_proj(ctx)
        ctx_up = F.interpolate(ctx_ori, size=x.shape[2:], mode='bilinear', align_corners=False)

        for idx, blk in enumerate(self.overlock.sub_blocks3):
            if idx == 0:
                ctx = ctx_up
            x, ctx = blk(x, ctx, ctx_up)

        x, ctx = self.overlock.patch_embedx(x, ctx)
        for idx, blk in enumerate(self.overlock.sub_blocks4):
            x, ctx = blk(x, ctx, ctx_ori)

        features.append(ctx)  # Stage 3 output - final context features

        return features
    
    def forward(self, x):
        """
        Forward function that mimics SFS backbone output format
        
        Args:
            x: Input tensor (B, 3, H, W)
            
        Returns:
            List of feature tensors at different scales, matching SFS backbone format
        """
        # Extract features from OverLoCK
        stage_features = self.extract_stage_features(x)
        
        # Project features to match expected dimensions and return only requested indices
        outs = []
        for i in self.out_indices:
            if i < len(stage_features):
                feat = stage_features[i]
                # Apply feature projection to match Swin dimensions
                feat = self.feature_projections[i](feat)
                outs.append(feat)
        
        return outs


def create_overlock_backbone(config):
    """
    Factory function to create OverLoCK backbone from config
    """
    return OverLoCKBackbone(**config)


# Test function
if __name__ == "__main__":
    # Test the OverLoCK backbone
    config = {
        'depth': [6, 6, 8, 3],
        'sub_depth': [16, 3],
        'in_chans': 3,
        'embed_dim': [64, 128, 320, 512],
        'kernel_size': [17, 15, 13, 7],
        'mlp_ratio': [4, 4, 4, 4],
        'sub_num_heads': [8, 16],
        'sub_mlp_ratio': [3, 3],
        'ls_init_value': [None, None, 1, 1],
        'res_scale': True,
        'smk_size': 5,
        'deploy': False,
        'use_gemm': True,
        'use_ds': False,
        'drop_rate': 0,
        'drop_path_rate': 0.2,
        'num_classes': 0,
        'use_checkpoint': [0, 0, 0, 0],
        'out_indices': (0, 1, 2, 3)
    }
    
    model = OverLoCKBackbone(**config)
    x = torch.randn(2, 3, 256, 256)
    
    with torch.no_grad():
        outputs = model(x)
    
    print("OverLoCK Backbone Test:")
    print(f"Input shape: {x.shape}")
    print(f"Number of output stages: {len(outputs)}")
    for i, out in enumerate(outputs):
        print(f"Stage {i} output shape: {out.shape}")
