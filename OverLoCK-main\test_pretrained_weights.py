#!/usr/bin/env python3
"""
测试预训练权重加载
"""

import os
import sys
import torch

def test_pretrained_weights():
    """测试预训练权重文件"""
    print("=== 测试预训练权重文件 ===")
    
    weight_path = 'overlock/overlock_s_in1k_224.pth'
    
    # 检查文件是否存在
    if not os.path.exists(weight_path):
        print(f"❌ 预训练权重文件不存在: {weight_path}")
        return False
    
    # 获取文件大小
    file_size = os.path.getsize(weight_path) / (1024 * 1024)  # MB
    print(f"✅ 预训练权重文件存在: {weight_path}")
    print(f"文件大小: {file_size:.2f} MB")
    
    # 尝试加载权重文件
    try:
        checkpoint = torch.load(weight_path, map_location='cpu')
        print("✅ 权重文件加载成功")
        
        # 分析权重文件内容
        if isinstance(checkpoint, dict):
            print("权重文件内容:")
            for key in checkpoint.keys():
                if key == 'model' or key == 'state_dict':
                    print(f"  {key}: 包含 {len(checkpoint[key])} 个参数")
                    # 显示前几个参数名
                    param_names = list(checkpoint[key].keys())[:5]
                    for name in param_names:
                        print(f"    - {name}")
                    if len(checkpoint[key]) > 5:
                        print(f"    - ... (还有 {len(checkpoint[key]) - 5} 个参数)")
                else:
                    print(f"  {key}: {type(checkpoint[key])}")
        else:
            print(f"权重文件类型: {type(checkpoint)}")
            if hasattr(checkpoint, 'keys'):
                print(f"包含 {len(checkpoint)} 个参数")
        
        return True
        
    except Exception as e:
        print(f"❌ 权重文件加载失败: {e}")
        return False

def test_config_loading():
    """测试配置文件中的权重加载设置"""
    print("\n=== 测试配置文件权重设置 ===")
    
    try:
        # 添加segmentation路径
        sys.path.insert(0, 'segmentation')
        
        try:
            from mmcv import Config
        except ImportError:
            from mmengine import Config
        
        # 加载配置文件
        config_path = 'segmentation/configs/overlock/upernet_overlock_small_pie_rgb_sar_8xb8.py'
        cfg = Config.fromfile(config_path)
        
        print("✅ 配置文件加载成功")
        
        # 检查预训练权重设置
        if hasattr(cfg, 'load_from') and cfg.load_from:
            print(f"✅ 配置了预训练权重路径: {cfg.load_from}")
            
            # 检查路径是否存在
            if os.path.exists(cfg.load_from):
                print("✅ 预训练权重文件路径有效")
            else:
                print("❌ 预训练权重文件路径无效")
                return False
        else:
            print("⚠️ 未配置预训练权重路径")
        
        # 检查backbone设置
        backbone_pretrained = cfg.model.backbone.get('pretrained', None)
        print(f"Backbone预训练设置: {backbone_pretrained}")
        
        if backbone_pretrained is True:
            print("⚠️ Backbone设置为自动下载预训练权重，可能与load_from冲突")
        elif backbone_pretrained is False:
            print("✅ Backbone设置为不自动下载，将使用load_from指定的权重")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置文件测试失败: {e}")
        return False

def test_weight_compatibility():
    """测试权重兼容性"""
    print("\n=== 测试权重兼容性 ===")
    
    try:
        # 添加路径
        sys.path.insert(0, 'segmentation')
        
        # 尝试导入模型
        from segmentation.models.overlock import overlock_s
        
        # 创建模型
        model = overlock_s(pretrained=False)
        print("✅ 模型创建成功")
        
        # 获取模型参数
        model_params = {name: param.shape for name, param in model.named_parameters()}
        print(f"模型参数数量: {len(model_params)}")
        
        # 加载预训练权重
        weight_path = 'overlock/overlock_s_in1k_224.pth'
        checkpoint = torch.load(weight_path, map_location='cpu')
        
        # 获取权重参数
        if 'model' in checkpoint:
            pretrained_params = checkpoint['model']
        elif 'state_dict' in checkpoint:
            pretrained_params = checkpoint['state_dict']
        else:
            pretrained_params = checkpoint
        
        print(f"预训练权重参数数量: {len(pretrained_params)}")
        
        # 检查参数匹配
        matched_params = 0
        mismatched_params = []
        
        for name, shape in model_params.items():
            if name in pretrained_params:
                if pretrained_params[name].shape == shape:
                    matched_params += 1
                else:
                    mismatched_params.append(f"{name}: 模型{shape} vs 权重{pretrained_params[name].shape}")
            else:
                mismatched_params.append(f"{name}: 模型中存在但权重中不存在")
        
        print(f"✅ 匹配的参数: {matched_params}/{len(model_params)}")
        
        if mismatched_params:
            print("⚠️ 不匹配的参数:")
            for mismatch in mismatched_params[:5]:  # 只显示前5个
                print(f"  - {mismatch}")
            if len(mismatched_params) > 5:
                print(f"  - ... (还有 {len(mismatched_params) - 5} 个不匹配)")
        
        # 尝试加载权重
        try:
            model.load_state_dict(pretrained_params, strict=False)
            print("✅ 权重加载成功 (非严格模式)")
            return True
        except Exception as e:
            print(f"❌ 权重加载失败: {e}")
            return False
        
    except Exception as e:
        print(f"❌ 兼容性测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("预训练权重测试工具")
    print("=" * 50)
    
    success = True
    
    # 测试权重文件
    success &= test_pretrained_weights()
    
    # 测试配置文件
    success &= test_config_loading()
    
    # 测试兼容性
    success &= test_weight_compatibility()
    
    print("\n" + "=" * 50)
    if success:
        print("✅ 预训练权重配置正确！可以开始训练。")
    else:
        print("❌ 预训练权重配置有问题！请检查配置。")
    
    return success

if __name__ == '__main__':
    main()
