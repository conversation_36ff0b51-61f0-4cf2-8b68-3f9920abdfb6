#!/usr/bin/env python3
"""
测试脚本：验证三主干神经网络的训练流程
"""

import torch
import torch.nn as nn
import sys
import os
import numpy as np

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import config
from models.builder import EncoderDecoder as segmodel
from utils.loss_opr import CombinedLoss


def create_dummy_data():
    """创建虚拟数据用于测试"""
    batch_size = config.batch_size
    height, width = config.image_height, config.image_width
    num_classes = config.num_classes
    
    # 创建虚拟输入数据
    rgb_data = torch.randn(batch_size, 3, height, width)
    modal_data = torch.randn(batch_size, 3, height, width)  # SAR数据
    
    # 创建虚拟标签数据
    labels = torch.randint(0, num_classes, (batch_size, height, width))
    
    return rgb_data, modal_data, labels


def test_training_step():
    """测试单个训练步骤"""
    print("=" * 60)
    print("测试三主干神经网络训练步骤")
    print("=" * 60)
    
    try:
        # 创建损失函数
        criterion = CombinedLoss(
            num_classes=config.num_classes,
            ignore_index=config.background,
            ce_weight=config.loss_weights['ce_weight'],
            focal_weight=config.loss_weights['focal_weight'],
            dice_weight=config.loss_weights['dice_weight'],
            lovasz_weight=config.loss_weights['lovasz_weight'],
            focal_gamma=config.focal_gamma,
            focal_alpha=config.focal_alpha,
            label_smoothing=config.label_smoothing
        )
        print("✓ 损失函数创建成功")
        
        # 创建模型
        model = segmodel(cfg=config, criterion=criterion, norm_layer=nn.BatchNorm2d)
        print("✓ 三主干模型创建成功")
        
        # 创建优化器
        optimizer = torch.optim.AdamW(
            model.parameters(),
            lr=config.lr,
            weight_decay=config.weight_decay
        )
        print("✓ 优化器创建成功")
        
        # 移动到GPU（如果可用）
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        model.to(device)
        print(f"✓ 模型移动到设备: {device}")
        
        # 创建虚拟数据
        rgb_data, modal_data, labels = create_dummy_data()
        rgb_data = rgb_data.to(device)
        modal_data = modal_data.to(device)
        labels = labels.to(device)
        
        print(f"✓ 数据准备完成:")
        print(f"  RGB数据形状: {rgb_data.shape}")
        print(f"  模态数据形状: {modal_data.shape}")
        print(f"  标签形状: {labels.shape}")
        
        # 执行前向传播
        model.train()
        optimizer.zero_grad()
        
        print("执行前向传播...")
        loss = model(rgb_data, modal_data, labels)
        print(f"✓ 前向传播成功，损失值: {loss.item():.6f}")
        
        # 执行反向传播
        print("执行反向传播...")
        loss.backward()
        print("✓ 反向传播成功")
        
        # 检查梯度
        total_norm = 0
        param_count = 0
        for p in model.parameters():
            if p.grad is not None:
                param_norm = p.grad.data.norm(2)
                total_norm += param_norm.item() ** 2
                param_count += 1
        total_norm = total_norm ** (1. / 2)
        print(f"✓ 梯度检查完成，梯度范数: {total_norm:.6f}, 参数数量: {param_count}")
        
        # 执行优化步骤
        if config.grad_clip > 0:
            torch.nn.utils.clip_grad_norm_(model.parameters(), config.grad_clip)
            print(f"✓ 梯度裁剪完成，阈值: {config.grad_clip}")
        
        optimizer.step()
        print("✓ 优化步骤完成")
        
        # 测试评估模式
        model.eval()
        with torch.no_grad():
            output = model(rgb_data, modal_data)
            if isinstance(output, tuple):
                main_output, aux_output = output
                print(f"✓ 评估模式测试成功:")
                print(f"  主输出形状: {main_output.shape}")
                print(f"  辅助输出形状: {aux_output.shape}")
            else:
                print(f"✓ 评估模式测试成功，输出形状: {output.shape}")
        
        return True
        
    except Exception as e:
        print(f"❌ 训练步骤测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_memory_usage():
    """测试内存使用情况"""
    print("\n" + "=" * 60)
    print("测试内存使用情况")
    print("=" * 60)
    
    if not torch.cuda.is_available():
        print("⚠ CUDA不可用，跳过GPU内存测试")
        return True
    
    try:
        # 清空GPU缓存
        torch.cuda.empty_cache()
        initial_memory = torch.cuda.memory_allocated()
        print(f"初始GPU内存使用: {initial_memory / 1024**2:.2f} MB")
        
        # 创建模型和数据
        criterion = nn.CrossEntropyLoss(ignore_index=config.background)
        model = segmodel(cfg=config, criterion=criterion, norm_layer=nn.BatchNorm2d)
        model.cuda()
        
        model_memory = torch.cuda.memory_allocated()
        print(f"模型加载后GPU内存使用: {model_memory / 1024**2:.2f} MB")
        print(f"模型占用内存: {(model_memory - initial_memory) / 1024**2:.2f} MB")
        
        # 创建数据
        rgb_data, modal_data, labels = create_dummy_data()
        rgb_data = rgb_data.cuda()
        modal_data = modal_data.cuda()
        labels = labels.cuda()
        
        data_memory = torch.cuda.memory_allocated()
        print(f"数据加载后GPU内存使用: {data_memory / 1024**2:.2f} MB")
        print(f"数据占用内存: {(data_memory - model_memory) / 1024**2:.2f} MB")
        
        # 前向传播
        model.train()
        loss = model(rgb_data, modal_data, labels)
        
        forward_memory = torch.cuda.memory_allocated()
        print(f"前向传播后GPU内存使用: {forward_memory / 1024**2:.2f} MB")
        print(f"前向传播占用内存: {(forward_memory - data_memory) / 1024**2:.2f} MB")
        
        # 反向传播
        loss.backward()
        
        backward_memory = torch.cuda.memory_allocated()
        print(f"反向传播后GPU内存使用: {backward_memory / 1024**2:.2f} MB")
        print(f"反向传播占用内存: {(backward_memory - forward_memory) / 1024**2:.2f} MB")
        
        max_memory = torch.cuda.max_memory_allocated()
        print(f"峰值GPU内存使用: {max_memory / 1024**2:.2f} MB")
        
        # 检查是否超出内存限制
        if max_memory > 8 * 1024**3:  # 8GB
            print("⚠ 内存使用超过8GB，建议减小批处理大小")
        else:
            print("✓ 内存使用在合理范围内")
        
        return True
        
    except Exception as e:
        print(f"❌ 内存测试失败: {e}")
        return False


def main():
    """主函数"""
    print("开始测试三主干神经网络训练流程...")
    
    # 测试训练步骤
    training_ok = test_training_step()
    
    # 测试内存使用
    memory_ok = test_memory_usage()
    
    print("\n" + "=" * 60)
    if training_ok and memory_ok:
        print("✓ 所有训练测试通过！")
        print("✓ 三主干神经网络可以正常训练！")
        print("✓ 可以运行 train.py 开始正式训练！")
        print("\n建议的训练命令:")
        print("python train.py")
    else:
        print("❌ 部分测试失败，请检查配置")
    print("=" * 60)


if __name__ == "__main__":
    main()
