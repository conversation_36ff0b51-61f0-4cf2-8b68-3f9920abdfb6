# PIE-RGB-SAR 语义分割训练指南

本指南介绍如何使用OverLoCK模型在PIE-RGB-SAR数据集上进行语义分割训练。

## 数据集信息

- **数据集名称**: PIE-RGB-SAR
- **任务类型**: 语义分割
- **输入**: RGB图像 (256x256x3)
- **输出**: 语义分割标签 (256x256)
- **类别数**: 4个类别 (标签值: 1, 2, 3, 4)
- **训练样本**: 2,432个
- **验证样本**: 2,433个

## 模型配置

- **Backbone**: OverLoCK-Small
- **上采样头**: UPernet
- **辅助头**: FCNnet
- **输入尺寸**: 256x256
- **批次大小**: 8 (每GPU)

## 文件结构

```
├── segmentation/
│   ├── configs/
│   │   ├── _base_/
│   │   │   ├── datasets/
│   │   │   │   └── pie_rgb_sar.py          # PIE-RGB-SAR数据集配置
│   │   │   └── models/
│   │   │       └── upernet_overlock_s.py   # OverLoCK+UPernet模型配置
│   │   └── overlock/
│   │       └── upernet_overlock_small_pie_rgb_sar_8xb8.py  # 完整训练配置
├── datasets/
│   └── PIE-RGB-SAR/
│       ├── RGB/           # RGB图像文件夹
│       ├── Label/         # 标签文件夹
│       ├── train.txt      # 训练集文件列表
│       └── val.txt        # 验证集文件列表
├── train_pie_rgb_sar.py   # 训练脚本
├── test_pie_rgb_sar.py    # 测试脚本
└── validate_dataset.py    # 数据集验证脚本
```

## 使用方法

### 1. 验证数据集

首先验证数据集配置是否正确：

```bash
python validate_dataset.py
```

### 2. 开始训练

#### 单GPU训练
```bash
python train_pie_rgb_sar.py
```

#### 多GPU训练
```bash
python train_pie_rgb_sar.py --gpus 4
```

#### 自定义参数
```bash
python train_pie_rgb_sar.py \
    --config segmentation/configs/overlock/upernet_overlock_small_pie_rgb_sar_8xb8.py \
    --work-dir work_dirs/my_experiment \
    --gpus 2 \
    --seed 42
```

### 3. 恢复训练

```bash
python train_pie_rgb_sar.py \
    --resume-from work_dirs/upernet_overlock_small_pie_rgb_sar/latest.pth
```

### 4. 测试模型

```bash
python test_pie_rgb_sar.py \
    --checkpoint work_dirs/upernet_overlock_small_pie_rgb_sar/latest.pth \
    --eval mIoU
```

### 5. 可视化结果

```bash
python test_pie_rgb_sar.py \
    --checkpoint work_dirs/upernet_overlock_small_pie_rgb_sar/latest.pth \
    --show-dir results/ \
    --opacity 0.5
```

## 训练配置详情

### 数据增强
- 随机缩放 (0.8-1.2倍)
- 随机裁剪 (256x256)
- 随机翻转 (50%概率)
- 光度变换

### 优化器配置
- **优化器**: AdamW
- **学习率**: 6e-5
- **权重衰减**: 0.01
- **学习率策略**: 多项式衰减
- **预热**: 1500次迭代

### 训练设置
- **总迭代数**: 80,000
- **检查点保存间隔**: 8,000次迭代
- **评估间隔**: 8,000次迭代
- **评估指标**: mIoU

## 预期结果

根据数据集分析，各类别分布如下：
- 类别 1: 73.13% (主要类别)
- 类别 2: 16.55%
- 类别 3: 1.32% (稀少类别)
- 类别 4: 9.00%

由于类别分布不均衡，建议关注以下指标：
- 整体mIoU
- 各类别IoU
- 稀少类别(类别3)的召回率

## 故障排除

### 常见问题

1. **CUDA内存不足**
   - 减少批次大小：修改配置文件中的`samples_per_gpu`
   - 使用梯度累积

2. **数据加载错误**
   - 运行`validate_dataset.py`检查数据集
   - 确认文件路径正确

3. **模型加载失败**
   - 检查OverLoCK模型是否正确注册
   - 确认预训练权重路径

### 性能优化

1. **加速训练**
   - 增加`workers_per_gpu`
   - 使用混合精度训练
   - 启用CUDNN benchmark

2. **提升精度**
   - 调整学习率
   - 增加训练迭代数
   - 尝试不同的数据增强策略

## 配置修改

如需修改训练配置，可以编辑以下文件：

- **数据集配置**: `segmentation/configs/_base_/datasets/pie_rgb_sar.py`
- **模型配置**: `segmentation/configs/_base_/models/upernet_overlock_s.py`
- **训练配置**: `segmentation/configs/overlock/upernet_overlock_small_pie_rgb_sar_8xb8.py`

## 注意事项

1. 确保数据集路径正确
2. PIE-RGB-SAR标签值从1开始，已设置`reduce_zero_label=False`
3. 模型输出类别数设置为4
4. 建议使用GPU进行训练，CPU训练会非常慢

## 联系支持

如有问题，请检查：
1. 数据集是否完整
2. 环境依赖是否安装
3. 配置文件是否正确
